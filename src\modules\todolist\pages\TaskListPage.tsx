import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, IconCard, Table, Tooltip } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { SortOrder, TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';

import TaskForm from '../components/TaskForm';
import VirtualizedTaskList from '../components/VirtualizedTaskList';
import { useDeleteTask, useTasks } from '../hooks/useTasks';
import { TaskDto, TaskPriority, TaskQueryDto, TaskStatus } from '../types/task.types';

import type { TFunction } from 'i18next';

// Cấu hình hiển thị cho các trạng thái
const getStatusConfig = (t: TFunction) => ({
  [TaskStatus.PENDING]: {
    color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
    text: t('todolist:task.status.todo'),
  },
  [TaskStatus.IN_PROGRESS]: {
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    text: t('todolist:task.status.inProgress'),
  },
  [TaskStatus.COMPLETED]: {
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    text: t('todolist:task.status.done'),
  },
  [TaskStatus.APPROVED]: {
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    text: t('todolist:task.status.done'),
  },
  [TaskStatus.REJECTED]: {
    color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    text: t('todolist:task.status.cancelled'),
  },
});

// Enum cho chế độ hiển thị
enum ViewMode {
  TABLE = 'table',
  LIST = 'list',
}

/**
 * Trang danh sách công việc sử dụng các hooks tối ưu
 */
const TaskListPage: React.FC = () => {
  const { t } = useTranslation(['todolist', 'common']);
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.TABLE);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Sử dụng hook xóa task
  const deleteTaskMutation = useDeleteTask();

  // Xử lý xóa task
  const handleDelete = useCallback(
    (id: number) => {
      if (
        window.confirm(t('todolist:task.confirmDelete', 'Bạn có chắc chắn muốn xóa công việc này?'))
      ) {
        deleteTaskMutation.mutate(id, {
          onSuccess: () => {
            NotificationUtil.success({
              message: t('todolist:task.deleteSuccess', 'Xóa công việc thành công'),
            });
          },
          onError: error => {
            console.error('Error deleting task:', error);
            NotificationUtil.error({
              message: t('todolist:task.deleteError', 'Xóa công việc thất bại'),
            });
          },
        });
      }
    },
    [deleteTaskMutation, t]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<TaskDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'title',
        title: t('todolist:task.table.title'),
        dataIndex: 'title',
        width: '20%',
        sortable: true,
      },
      {
        key: 'status',
        title: t('todolist:task.table.status'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as TaskStatus;
          const statusConfig = getStatusConfig(t);

          // Fallback nếu status không tồn tại trong cấu hình
          const config = statusConfig[status] || statusConfig[TaskStatus.PENDING];

          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${config.color}`}
            >
              {config.text}
            </div>
          );
        },
      },
      {
        key: 'priority',
        title: t('todolist:task.table.priority'),
        dataIndex: 'priority',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const priority = value as TaskPriority;
          const priorityConfig = {
            [TaskPriority.LOW]: {
              color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
              text: t('todolist:task.priority.low'),
            },
            [TaskPriority.MEDIUM]: {
              color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
              text: t('todolist:task.priority.medium'),
            },
            [TaskPriority.HIGH]: {
              color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
              text: t('todolist:task.priority.high'),
            },
            [TaskPriority.URGENT]: {
              color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
              text: t('todolist:task.priority.urgent'),
            },
          };

          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${priorityConfig[priority]?.color || priorityConfig[TaskPriority.MEDIUM].color}`}
            >
              {priorityConfig[priority]?.text || priorityConfig[TaskPriority.MEDIUM].text}
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('todolist:task.table.createdAt'),
        dataIndex: 'createdAt',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          if (!value) {
            return <div>-</div>;
          }
          const date = new Date(value as number);
          return <div>{date.toLocaleDateString()}</div>;
        },
      },
      {
        key: 'assigneeId',
        title: t('todolist:task.table.assignee'),
        dataIndex: 'assigneeId',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          if (!value) {
            return <div>-</div>;
          }
          return <div>ID: {String(value)}</div>;
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '10%',
        render: (_: unknown, record: TaskDto) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common:view')} position="top">
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => navigate(`/todolist/tasks/${record.id}`)}
              />
            </Tooltip>
            <Tooltip content={t('common:edit')} position="top">
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => navigate(`/todolist/tasks/${record.id}`)}
              />
            </Tooltip>
            <Tooltip content={t('common:delete')} position="top">
              <IconCard
                icon="trash"
                variant="default"
                size="sm"
                onClick={() => handleDelete(record.id)}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handleDelete, navigate]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      {
        id: 'todo',
        label: t('todolist:task.status.todo'),
        icon: 'clock',
        value: TaskStatus.PENDING,
      },
      {
        id: 'inProgress',
        label: t('todolist:task.status.inProgress'),
        icon: 'loading',
        value: TaskStatus.IN_PROGRESS,
      },
      {
        id: 'done',
        label: t('todolist:task.status.done'),
        icon: 'check',
        value: TaskStatus.COMPLETED,
      },
      {
        id: 'cancelled',
        label: t('todolist:task.status.cancelled'),
        icon: 'x',
        value: TaskStatus.REJECTED,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): TaskQueryDto => {
    const queryParams: TaskQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all' && typeof params.filterValue === 'string') {
      queryParams.status = params.filterValue as TaskStatus;
    }

    // Thêm xử lý dateRange nếu có
    if (params.dateRange && params.dateRange[0] && params.dateRange[1]) {
      queryParams.startDate = params.dateRange[0].getTime();
      queryParams.endDate = params.dateRange[1].getTime();
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<TaskDto, TaskQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Lấy danh sách công việc từ API
  const { data: tasksData, isLoading } = useTasks(dataTable.queryParams);

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = () => {
    hideForm();
    // Refresh data by refetching the tasks
    dataTable.tableData.handlePageChange(1, dataTable.tableData.pageSize);
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [TaskStatus.PENDING]: t('todolist:task.status.todo'),
      [TaskStatus.IN_PROGRESS]: t('todolist:task.status.inProgress'),
      [TaskStatus.COMPLETED]: t('todolist:task.status.done'),
      [TaskStatus.APPROVED]: t('todolist:task.status.done'),
      [TaskStatus.REJECTED]: t('todolist:task.status.cancelled'),
    },
    t,
  });

  // Xử lý thay đổi chế độ hiển thị
  const handleViewModeChange = useCallback((mode: ViewMode) => {
    setViewMode(mode);
  }, []);

  // Tạo additional icons cho MenuIconBar
  const additionalIcons = useMemo(
    () => [
      {
        icon: 'list' as const,
        tooltip: t('todolist:task.viewMode.table', 'Bảng'),
        variant: (viewMode === ViewMode.TABLE ? 'primary' : 'default') as
          | 'default'
          | 'primary'
          | 'secondary'
          | 'ghost',
        onClick: () => handleViewModeChange(ViewMode.TABLE),
      },
      {
        icon: 'grid' as const,
        tooltip: t('todolist:task.viewMode.list', 'Danh sách'),
        variant: (viewMode === ViewMode.LIST ? 'primary' : 'default') as
          | 'default'
          | 'primary'
          | 'secondary'
          | 'ghost',
        onClick: () => handleViewModeChange(ViewMode.LIST),
      },
    ],
    [viewMode, t, handleViewModeChange]
  );

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
        additionalIcons={additionalIcons}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <SlideInForm isVisible={isVisible}>
        <TaskForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        {viewMode === ViewMode.TABLE ? (
          <Table
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={tasksData?.items ?? []}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: tasksData?.meta.currentPage || 1,
              pageSize: dataTable.tableData.pageSize,
              total: tasksData?.meta.totalItems || 0,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 15, 20],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        ) : (
          <VirtualizedTaskList
            tasks={tasksData?.items ?? []}
            isLoading={isLoading}
            onRefresh={() => dataTable.tableData.handlePageChange(1, dataTable.tableData.pageSize)}
            maxHeight={600}
          />
        )}
      </Card>
    </div>
  );
};

export default TaskListPage;
