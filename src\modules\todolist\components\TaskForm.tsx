import React from 'react';
import { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as z from 'zod';

import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Loading,
  Select,
  Textarea,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { useFormErrors } from '@/shared/hooks';
import { NotificationUtil } from '@/shared/utils/notification';

import { useProjects } from '../hooks/useProjects';
import { useCreateTask, useUpdateTask } from '../hooks/useTasks';
import { TaskDto, TaskPriority } from '../types/task.types';

// Định nghĩa schema validation
const taskSchema = z.object({
  title: z
    .string()
    .min(1, 'Tiêu đề công việc không được để trống')
    .max(255, 'Tiêu đề công việc không được vượt quá 255 ký tự'),
  description: z.string().max(1000, '<PERSON><PERSON> tả không được vượt quá 1000 ký tự').optional().nullable(),
  assigneeId: z
    .string()
    .optional()
    .nullable()
    .transform(val => (val ? parseInt(val, 10) : null)),
  priority: z
    .string()
    .transform(val => {
      // Chuyển đổi string thành TaskPriority enum
      switch (val) {
        case TaskPriority.LOW:
          return TaskPriority.LOW;
        case TaskPriority.MEDIUM:
          return TaskPriority.MEDIUM;
        case TaskPriority.HIGH:
          return TaskPriority.HIGH;
        case TaskPriority.URGENT:
          return TaskPriority.URGENT;
        default:
          return TaskPriority.MEDIUM;
      }
    })
    .optional()
    .nullable(),
  expectedStars: z
    .number()
    .min(1, 'Số sao kỳ vọng phải từ 1 đến 5')
    .max(5, 'Số sao kỳ vọng phải từ 1 đến 5')
    .optional()
    .nullable(),
  categoryId: z
    .string()
    .optional()
    .nullable()
    .transform(val => (val ? parseInt(val, 10) : null)),
  parentId: z
    .string()
    .optional()
    .nullable()
    .transform(val => (val ? parseInt(val, 10) : null)),
});

// Kiểu dữ liệu form
type TaskFormValues = {
  title: string;
  description?: string | null;
  assigneeId: string;
  priority?: TaskPriority | null;
  expectedStars?: number | null;
  categoryId: string;
  parentId: string;
};

// Props component
interface TaskFormProps {
  task?: TaskDto;
  parentId?: number;
  onSubmit: () => void;
  onCancel: () => void;
}

/**
 * Form tạo/cập nhật công việc
 */
const TaskForm: React.FC<TaskFormProps> = ({ task, parentId, onSubmit, onCancel }) => {
  const { t } = useTranslation(['common', 'todolist']);
  const isEditMode = !!task;

  // Lấy danh sách dự án
  const { data: projectsData, isLoading: isLoadingProjects } = useProjects();

  // Hooks mutation
  const { mutateAsync: createTask, isPending: isCreating } = useCreateTask();
  const { mutateAsync: updateTask, isPending: isUpdating } = useUpdateTask();

  // Form hook with error handling
  const { formRef, setFormErrors } = useFormErrors<TaskFormValues>();

  // Xử lý submit form
  const handleSubmit = async (values: unknown) => {
    // Type assertion to TaskFormValues
    const taskValues = values as TaskFormValues;

    try {
      // Reset form errors
      setFormErrors({});

      if (isEditMode && task) {
        await updateTask({
          id: task.id,
          data: {
            title: taskValues.title,
            description: taskValues.description || undefined,
            assigneeId: taskValues.assigneeId ? parseInt(taskValues.assigneeId, 10) : undefined,
            priority: taskValues.priority || undefined,
            expectedStars: taskValues.expectedStars || undefined,
            categoryId: taskValues.categoryId ? parseInt(taskValues.categoryId, 10) : undefined,
            parentId: taskValues.parentId ? parseInt(taskValues.parentId, 10) : undefined,
          },
        });
        NotificationUtil.success({
          message: t('todolist:task.notifications.updateSuccess', 'Cập nhật công việc thành công'),
        });
      } else {
        await createTask({
          title: taskValues.title,
          description: taskValues.description || undefined,
          assigneeId: taskValues.assigneeId ? parseInt(taskValues.assigneeId, 10) : undefined,
          priority: taskValues.priority || undefined,
          expectedStars: taskValues.expectedStars || undefined,
          categoryId: taskValues.categoryId ? parseInt(taskValues.categoryId, 10) : undefined,
          parentId: taskValues.parentId ? parseInt(taskValues.parentId, 10) : undefined,
        });
        NotificationUtil.success({
          message: t('todolist:task.notifications.createSuccess', 'Tạo công việc thành công'),
        });
      }
      onSubmit();
    } catch (error) {
      console.error('Error submitting task form:', error);

      // Check if error has field-specific errors
      if (error && typeof error === 'object' && 'response' in error && error.response) {
        const axiosError = error as {
          response: { data?: { message?: string; errors?: Record<string, string> } };
        };

        // If there are field-specific errors, set them
        if (axiosError.response.data?.errors) {
          setFormErrors(axiosError.response.data.errors);
        } else {
          // Otherwise set a general error
          NotificationUtil.error({
            message: isEditMode
              ? t('todolist:task.notifications.updateError', 'Lỗi khi cập nhật công việc')
              : t('todolist:task.notifications.createError', 'Lỗi khi tạo công việc'),
          });
        }
      } else {
        // For other errors, show a general notification
        NotificationUtil.error({
          message: isEditMode
            ? t('todolist:task.notifications.updateError', 'Lỗi khi cập nhật công việc')
            : t('todolist:task.notifications.createError', 'Lỗi khi tạo công việc'),
        });
      }
    }
  };

  // Danh sách mức độ ưu tiên
  const priorityOptions = [
    { value: TaskPriority.LOW, label: t('todolist:task.priority.low', 'Thấp') },
    { value: TaskPriority.MEDIUM, label: t('todolist:task.priority.medium', 'Trung bình') },
    { value: TaskPriority.HIGH, label: t('todolist:task.priority.high', 'Cao') },
    { value: TaskPriority.URGENT, label: t('todolist:task.priority.urgent', 'Khẩn cấp') },
  ];

  // Show loading state when projects are loading
  if (isLoadingProjects) {
    return (
      <Card className="p-4">
        <div className="py-4 flex justify-center items-center">
          <Loading size="md" />
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <Form
        ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
        schema={taskSchema}
        onSubmit={handleSubmit}
        className="space-y-6"
        defaultValues={{
          title: task?.title || '',
          description: task?.description || '',
          assigneeId: task?.assigneeId ? task.assigneeId.toString() : '',
          priority: task?.priority || TaskPriority.MEDIUM,
          expectedStars: task?.expectedStars || 3,
          categoryId: task?.categoryId ? task.categoryId.toString() : '',
          parentId: parentId ? parentId.toString() : task?.parentId ? task.parentId.toString() : '',
        }}
      >
        <FormItem name="title" label={t('todolist:task.fields.title', 'Tiêu đề')} required>
          <Input
            placeholder={t('todolist:task.placeholders.title', 'Nhập tiêu đề công việc')}
            fullWidth
          />
        </FormItem>

        <FormItem name="description" label={t('todolist:task.fields.description', 'Mô tả')}>
          <Textarea
            placeholder={t('todolist:task.placeholders.description', 'Nhập mô tả công việc')}
            rows={4}
          />
        </FormItem>

        <div className="grid grid-cols-12 gap-4">
          <FormItem
            name="priority"
            label={t('todolist:task.fields.priority', 'Mức độ ưu tiên')}
            className="col-span-8"
          >
            <Select
              placeholder={t('todolist:task.placeholders.priority', 'Chọn mức độ ưu tiên')}
              options={priorityOptions}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="expectedStars"
            label={t('todolist:task.fields.expectedStars', 'Số sao kỳ vọng')}
            className="col-span-4"
          >
            <Input
              type="number"
              min={1}
              max={5}
              placeholder={t(
                'todolist:task.placeholders.expectedStars',
                'Nhập số sao kỳ vọng (1-5)'
              )}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="categoryId" label={t('todolist:task.fields.project', 'Dự án')}>
          <Select
            placeholder={t('todolist:task.placeholders.project', 'Chọn dự án')}
            options={
              projectsData?.items.map(project => ({
                value: project.id.toString(),
                label: project.title,
              })) || []
            }
            loading={isLoadingProjects}
          />
        </FormItem>

        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isCreating || isUpdating}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" isLoading={isCreating || isUpdating}>
            {isEditMode ? t('common:update', 'Cập nhật') : t('common:create', 'Tạo mới')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default TaskForm;
